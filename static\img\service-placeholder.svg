<svg width="400" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="serviceBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e9ecef;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#serviceBg)"/>
  <rect x="20" y="20" width="360" height="160" fill="none" stroke="#dee2e6" stroke-width="2" stroke-dasharray="5,5" rx="10"/>
  <circle cx="200" cy="80" r="25" fill="#6c757d"/>
  <rect x="150" y="120" width="100" height="15" fill="#adb5bd" rx="7"/>
  <rect x="175" y="145" width="50" height="10" fill="#ced4da" rx="5"/>
  <text x="200" y="185" text-anchor="middle" fill="#6c757d" font-family="Arial, sans-serif" font-size="12">Hizmet Fotoğrafı</text>
</svg>
