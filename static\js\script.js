// Modern Navbar Scroll Effect
window.addEventListener("scroll", function () {
  const navbar = document.querySelector(".navbar");
  if (window.scrollY > 50) {
    navbar.classList.add("scrolled");
  } else {
    navbar.classList.remove("scrolled");
  }
});

// Active Link Highlighting
document.addEventListener('DOMContentLoaded', function() {
  const currentLocation = location.pathname;
  const menuItems = document.querySelectorAll('.navbar-nav .nav-link');

  menuItems.forEach(item => {
    if(item.getAttribute('href') === currentLocation) {
      item.classList.add('active');
    }
  });
});

// Smooth Dropdown Animation
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('.dropdown').forEach(dropdown => {
    dropdown.addEventListener('show.bs.dropdown', function() {
      const menu = this.querySelector('.dropdown-menu');
      menu.style.opacity = '0';
      menu.style.transform = 'translateY(-10px)';

      setTimeout(() => {
        menu.style.transition = 'all 0.3s ease';
        menu.style.opacity = '1';
        menu.style.transform = 'translateY(0)';
      }, 10);
    });
  });
});

// Mobile Menu Close on Link Click
document.addEventListener('DOMContentLoaded', function() {
  const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
  const navbarCollapse = document.querySelector('.navbar-collapse');

  navLinks.forEach(link => {
    link.addEventListener('click', () => {
      if (navbarCollapse.classList.contains('show')) {
        const bsCollapse = new bootstrap.Collapse(navbarCollapse);
        bsCollapse.hide();
      }
    });
  });
});

// Sayfa yüklenirken yumuşak geçiş
window.addEventListener("load", () => {
  document.body.classList.add('loaded');

  // Loading animasyonunu gizle
  const loading = document.querySelector('.loading');
  if (loading) {
    setTimeout(() => {
      loading.classList.add('hidden');
    }, 500);
  }
});

// Smooth scroll için anchor linkler
document.addEventListener('DOMContentLoaded', function() {
  const links = document.querySelectorAll('a[href^="#"]');

  links.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();

      const targetId = this.getAttribute('href');
      const targetSection = document.querySelector(targetId);

      if (targetSection) {
        targetSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
});

// Form validasyonu
function validateForm(form) {
  const requiredFields = form.querySelectorAll('[required]');
  let isValid = true;

  requiredFields.forEach(field => {
    if (!field.value.trim()) {
      field.style.borderColor = '#ef4444';
      isValid = false;
    } else {
      field.style.borderColor = '#e5e7eb';
    }
  });

  return isValid;
}

// Telefon numarası formatı
function formatPhoneNumber(input) {
  let value = input.value.replace(/\D/g, '');

  if (value.length >= 10) {
    value = value.replace(/(\d{4})(\d{3})(\d{2})(\d{2})/, '$1 $2 $3 $4');
  }

  input.value = value;
}

// Scroll to top butonu kaldırıldı - artık base.html'de floating butonlar var

// Lazy loading için intersection observer
function setupLazyLoading() {
  const images = document.querySelectorAll('img[data-src]');

  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.classList.remove('lazy');
        imageObserver.unobserve(img);
      }
    });
  });

  images.forEach(img => imageObserver.observe(img));
}

// Sayfa yüklendiğinde lazy loading'i başlat
document.addEventListener('DOMContentLoaded', setupLazyLoading);

// Animasyon tetikleyicisi
function setupAnimationTriggers() {
  const animatedElements = document.querySelectorAll('[data-aos]');

  const animationObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('aos-animate');
      }
    });
  }, {
    threshold: 0.1
  });

  animatedElements.forEach(el => animationObserver.observe(el));
}

// Sayfa yüklendiğinde animasyonları başlat
document.addEventListener('DOMContentLoaded', setupAnimationTriggers);

// WhatsApp butonu kaldırıldı - artık base.html'de floating butonlar var
