#!/bin/bash

# Simple startup script for Şenkal Grup website
# This script starts the Flask application directly with Python

set -e  # Exit on any error

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "Starting Şenkal Grup website (Simple Mode)..."
echo "Working directory: $(pwd)"

# Check if virtual environment exists and activate it
if [ -d "venv" ]; then
    echo "Activating virtual environment..."
    source venv/bin/activate
elif [ -d ".venv" ]; then
    echo "Activating virtual environment..."
    source .venv/bin/activate
fi

# Install Flask if not installed
python3 -c "import flask" 2>/dev/null || pip install flask

echo "Starting Flask development server..."
python3 app.py
