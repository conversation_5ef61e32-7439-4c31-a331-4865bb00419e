#!/bin/bash

# Şenkal Grup Website Startup Script
# This script starts the Flask application using Gunicorn

set -e  # Exit on any error

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "Starting Şenkal Grup website..."
echo "Working directory: $(pwd)"
echo "Python version: $(python3 --version)"

# Check if virtual environment exists and activate it
if [ -d "venv" ]; then
    echo "Activating virtual environment..."
    source venv/bin/activate
elif [ -d ".venv" ]; then
    echo "Activating virtual environment..."
    source .venv/bin/activate
else
    echo "No virtual environment found. Using system Python."
fi

# Install dependencies if requirements.txt exists
if [ -f "requirements.txt" ]; then
    echo "Installing/updating dependencies..."
    pip install -r requirements.txt
fi

# Check if app.py exists
if [ ! -f "app.py" ]; then
    echo "Error: app.py not found in $(pwd)"
    exit 1
fi

# Check if wsgi.py exists
if [ ! -f "wsgi.py" ]; then
    echo "Error: wsgi.py not found in $(pwd)"
    exit 1
fi

echo "Files in current directory:"
ls -la

# Test import before starting server
echo "Testing Python import..."
python3 -c "
import sys
import os
sys.path.insert(0, '.')
print('Python path:', sys.path)
print('Current directory:', os.getcwd())
print('Files:', os.listdir('.'))
try:
    from app import app
    print('✓ Successfully imported Flask app')
    print('✓ App object:', app)
except Exception as e:
    print('✗ Error importing app:', e)
    sys.exit(1)
"

if [ $? -ne 0 ]; then
    echo "Import test failed. Exiting."
    exit 1
fi

echo "Starting Gunicorn server..."

# Start with Gunicorn using the configuration file
exec gunicorn \
    --config gunicorn.conf.py \
    --chdir "$SCRIPT_DIR" \
    wsgi:application
