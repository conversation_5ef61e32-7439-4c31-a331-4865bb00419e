# Flask Core Framework
Flask==2.3.3
Werkzeug==2.3.7

# Template Engine (included with Flask but specified for clarity)
Jinja2==3.1.2
MarkupSafe==2.1.3

# WSGI Server for Production
gunicorn==21.2.0

# Database Support
Flask-SQLAlchemy==3.0.5
SQLAlchemy==2.0.21

# Database Migrations
Flask-Migrate==4.0.5
Alembic==1.12.0

# Forms and Validation
Flask-WTF==1.1.1
WTForms==3.0.1

# User Authentication and Session Management
Flask-Login==0.6.3
Flask-Session==0.5.0

# Password Hashing
bcrypt==4.0.1
Flask-Bcrypt==1.0.1

# Email Support
Flask-Mail==0.9.1

# Environment Variables
python-dotenv==1.0.0

# File Upload Security
secure-filename==0.1

# Date and Time Utilities
python-dateutil==2.8.2

# Image Processing (for file uploads)
Pillow==10.0.1

# HTTP Requests (for external APIs if needed)
requests==2.31.0

# JSON Web Tokens (if implementing JWT auth)
PyJWT==2.8.0

# URL Safe Serialization (for password reset tokens)
itsdangerous==2.1.2

# Click (Flask CLI support)
click==8.1.7

# Development and Testing Dependencies
pytest==7.4.2
pytest-flask==1.2.0
coverage==7.3.2

# Code Quality
flake8==6.1.0
black==23.9.1

# Security Headers
Flask-Talisman==1.1.0

# Rate Limiting
Flask-Limiter==3.5.0

# CORS Support (if building API)
Flask-CORS==4.0.0

# Caching
Flask-Caching==2.1.0

# Admin Interface (optional)
Flask-Admin==1.6.1

# Database Drivers
# SQLite (built into Python)
# PostgreSQL (uncomment if using PostgreSQL)
# psycopg2-binary==2.9.7

# MySQL (uncomment if using MySQL)
# PyMySQL==1.1.0

# Production Server Alternatives
# uWSGI==2.0.22  # Alternative to gunicorn
# waitress==2.1.2  # Windows-friendly WSGI server

# Monitoring and Logging
# Sentry-SDK==1.32.0  # Error tracking
# Flask-APM==0.1.0  # Application performance monitoring
