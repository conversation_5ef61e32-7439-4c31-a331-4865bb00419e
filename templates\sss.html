{% extends "base.html" %}

{% block title %}Sık Sorulan Sorular | Şenkal Grup - Merak <PERSON>{% endblock %}

{% block description %}Şenkal Grup hizmetleri hakkında sık sorulan sorular ve cevapları. Klima bakımı, kombi servisi, inş<PERSON>t süreçleri, fiyatlar ve garanti koşulları hakkında detaylı bilgiler.{% endblock %}

{% block keywords %}sık sorulan sorular, klima bakımı, kombi servisi, inşaat süreçleri, fiyat teklifi, garanti, SSS{% endblock %}

{% block content %}

<!-- Compact Hero Section -->
<section class="hero-section py-3 text-white text-center position-relative overflow-hidden" style="background: linear-gradient(135deg, rgba(99, 102, 241, 0.8) 0%, rgba(79, 70, 229, 0.8) 50%, rgba(67, 56, 202, 0.8) 100%), url('{{ url_for('static', filename='img/soruisareti.png') }}') center/cover no-repeat; margin-top: -75px; padding-top: 90px !important; min-height: 40vh;">

  <!-- Grid Pattern Overlay -->
  <div class="position-absolute top-0 start-0 w-100 h-100" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0); background-size: 30px 30px; opacity: 0.3;"></div>

  <div class="container position-relative" style="z-index: 2;">
    <div class="row justify-content-center">
      <div class="col-lg-8 col-xl-6" data-aos="fade-up">

        <div class="service-icon mx-auto mb-2 rounded-3 d-flex align-items-center justify-content-center" style="background: rgba(255,255,255,0.2); width: 60px; height: 60px; backdrop-filter: blur(10px);">
          <i class="bi bi-question-circle fs-3 text-white"></i>
        </div>

        <div class="badge-text mb-2" style="background: linear-gradient(135deg, #fbbf24, #f59e0b); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-size: 0.8rem; font-weight: 700; letter-spacing: 1px;">
          SIK SORULAN SORULAR
        </div>

        <h1 class="h2 fw-bold mb-2 lh-sm">
          Merak Ettiklerinizin <br class="d-none d-md-block">
          <span style="color: #fbbf24;">Cevapları</span>
        </h1>

        <p class="mb-3" style="opacity: 0.9; max-width: 400px; margin: 0 auto; font-size: 0.95rem;">
          Hizmetlerimiz hakkında <strong style="color: #fbbf24;">sık sorulan sorular</strong> ve detaylı cevapları.
        </p>

        <!-- CTA Button -->
        <div class="mt-3">
          <a href="tel:05301799558" class="btn btn-lg fw-bold px-4 py-2 rounded-4" style="background: linear-gradient(135deg, #fbbf24, #f59e0b); color: #111827; box-shadow: 0 10px 25px rgba(251, 191, 36, 0.4);">
            <i class="bi bi-telephone-fill me-2"></i>Hemen Arayın
          </a>
        </div>

      </div>
    </div>
  </div>
</section>

<!-- Modern FAQ Kategorileri -->
<section class="py-4" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
  <div class="container">
    <div class="text-center mb-4" data-aos="fade-up">
      <h2 class="h3 fw-bold text-dark mb-2">Soru Kategorileri</h2>
      <p class="text-muted mb-0" style="max-width: 500px; margin: 0 auto;">
        Kategorilere göre soruları inceleyebilirsiniz
      </p>
    </div>

    <div class="row g-4">
      <div class="col-md-6 col-lg-3" data-aos="fade-up">
        <div class="category-card h-100 p-4 rounded-4 text-center position-relative overflow-hidden" style="background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05)); border: 2px solid rgba(59, 130, 246, 0.2); transition: all 0.3s ease;">
          <div class="category-icon mb-3 mx-auto d-flex align-items-center justify-content-center" style="width: 70px; height: 70px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 50%; box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);">
            <i class="bi bi-snow text-white fs-3"></i>
          </div>
          <h4 class="fw-bold text-dark mb-2">Klima Hizmetleri</h4>
          <p class="text-muted small mb-0">Bakım, montaj, arıza giderme</p>
        </div>
      </div>

      <div class="col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="100">
        <div class="category-card h-100 p-4 rounded-4 text-center position-relative overflow-hidden" style="background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05)); border: 2px solid rgba(239, 68, 68, 0.2); transition: all 0.3s ease;">
          <div class="category-icon mb-3 mx-auto d-flex align-items-center justify-content-center" style="width: 70px; height: 70px; background: linear-gradient(135deg, #ef4444, #dc2626); border-radius: 50%; box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);">
            <i class="bi bi-fire text-white fs-3"></i>
          </div>
          <h4 class="fw-bold text-dark mb-2">Kombi Hizmetleri</h4>
          <p class="text-muted small mb-0">Bakım, onarım, montaj</p>
        </div>
      </div>

      <div class="col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="200">
        <div class="category-card h-100 p-4 rounded-4 text-center position-relative overflow-hidden" style="background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05)); border: 2px solid rgba(16, 185, 129, 0.2); transition: all 0.3s ease;">
          <div class="category-icon mb-3 mx-auto d-flex align-items-center justify-content-center" style="width: 70px; height: 70px; background: linear-gradient(135deg, #10b981, #059669); border-radius: 50%; box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);">
            <i class="bi bi-building text-white fs-3"></i>
          </div>
          <h4 class="fw-bold text-dark mb-2">İnşaat & Tadilat</h4>
          <p class="text-muted small mb-0">Proje süreçleri, malzemeler</p>
        </div>
      </div>

      <div class="col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="300">
        <div class="category-card h-100 p-4 rounded-4 text-center position-relative overflow-hidden" style="background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05)); border: 2px solid rgba(245, 158, 11, 0.2); transition: all 0.3s ease;">
          <div class="category-icon mb-3 mx-auto d-flex align-items-center justify-content-center" style="width: 70px; height: 70px; background: linear-gradient(135deg, #f59e0b, #d97706); border-radius: 50%; box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);">
            <i class="bi bi-currency-dollar text-white fs-3"></i>
          </div>
          <h4 class="fw-bold text-dark mb-2">Fiyat & Garanti</h4>
          <p class="text-muted small mb-0">Ödeme, garanti koşulları</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Arama Bölümü -->
<section class="py-4" style="background: white;">
  <div class="container">
    <div class="row justify-content-center" data-aos="fade-up">
      <div class="col-lg-8 col-xl-6">
        <div class="search-box p-4 rounded-4 text-center" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border: 2px solid rgba(99, 102, 241, 0.1);">
          <div class="mb-3">
            <i class="bi bi-search fs-2 text-primary mb-2"></i>
            <h3 class="h4 fw-bold text-dark mb-2">Soru Ara</h3>
            <p class="text-muted small mb-0">Aradığınız soruyu hızlıca bulun</p>
          </div>

          <div class="position-relative">
            <input type="text" id="searchInput" class="form-control form-control-lg rounded-4 ps-5" placeholder="Örn: klima bakımı, kombi arızası, fiyat..." style="border: 2px solid rgba(99, 102, 241, 0.2); box-shadow: 0 4px 15px rgba(0,0,0,0.05);">
            <i class="bi bi-search position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
          </div>

          <div class="mt-3">
            <div class="d-flex flex-wrap gap-2 justify-content-center">
              <span class="badge bg-light text-dark px-3 py-2 rounded-pill search-tag" style="cursor: pointer; border: 1px solid #e5e7eb;" onclick="searchFAQ('bakım')">
                <i class="bi bi-gear me-1"></i>Bakım
              </span>
              <span class="badge bg-light text-dark px-3 py-2 rounded-pill search-tag" style="cursor: pointer; border: 1px solid #e5e7eb;" onclick="searchFAQ('montaj')">
                <i class="bi bi-tools me-1"></i>Montaj
              </span>
              <span class="badge bg-light text-dark px-3 py-2 rounded-pill search-tag" style="cursor: pointer; border: 1px solid #e5e7eb;" onclick="searchFAQ('arıza')">
                <i class="bi bi-exclamation-triangle me-1"></i>Arıza
              </span>
              <span class="badge bg-light text-dark px-3 py-2 rounded-pill search-tag" style="cursor: pointer; border: 1px solid #e5e7eb;" onclick="searchFAQ('fiyat')">
                <i class="bi bi-currency-dollar me-1"></i>Fiyat
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Modern FAQ Accordion -->
<section class="py-5" style="background: white;">
  <div class="container">
    <!-- Arama Sonuçları -->
    <div id="searchResults" class="row justify-content-center mb-4" style="display: none;">
      <div class="col-lg-10 col-xl-8">
        <div class="alert alert-info rounded-4 d-flex align-items-center" style="background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05)); border: 2px solid rgba(59, 130, 246, 0.2);">
          <i class="bi bi-info-circle me-2 text-primary"></i>
          <span id="searchResultText" class="fw-semibold text-primary"></span>
          <button type="button" class="btn-close ms-auto" onclick="clearSearch()"></button>
        </div>
      </div>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-10 col-xl-8">

        <!-- FAQ Navigation -->
        <div class="faq-navigation mb-4" data-aos="fade-up">
          <div class="d-flex flex-wrap gap-2 justify-content-center">
            <button class="btn btn-outline-primary btn-sm rounded-pill px-3 category-filter active" data-category="all">
              <i class="bi bi-grid-3x3-gap me-1"></i>Tümü
            </button>
            <button class="btn btn-outline-primary btn-sm rounded-pill px-3 category-filter" data-category="klima">
              <i class="bi bi-snow me-1"></i>Klima
            </button>
            <button class="btn btn-outline-primary btn-sm rounded-pill px-3 category-filter" data-category="kombi">
              <i class="bi bi-fire me-1"></i>Kombi
            </button>
            <button class="btn btn-outline-primary btn-sm rounded-pill px-3 category-filter" data-category="insaat">
              <i class="bi bi-building me-1"></i>İnşaat
            </button>
            <button class="btn btn-outline-primary btn-sm rounded-pill px-3 category-filter" data-category="fiyat">
              <i class="bi bi-currency-dollar me-1"></i>Fiyat
            </button>
          </div>
        </div>

        <div id="faqContainer">

        <!-- Klima Soruları -->
        <div class="mb-5" data-aos="fade-up">
          <div class="d-flex align-items-center mb-4">
            <div class="category-icon me-3 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 12px;">
              <i class="bi bi-snow text-white fs-5"></i>
            </div>
            <h3 class="h3 fw-bold text-primary mb-0">Klima Hizmetleri</h3>
          </div>

          <div class="accordion" id="klimaAccordion">
            <div class="accordion-item border-0 mb-3 rounded-4 overflow-hidden" style="box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
              <h2 class="accordion-header">
                <button class="accordion-button collapsed fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#klima1" style="background: white; border: none; padding: 20px; font-size: 1.1rem; color: #111827;">
                  <i class="bi bi-calendar-check me-3 text-primary"></i>
                  Klima bakımı ne sıklıkla yapılmalıdır?
                </button>
              </h2>
              <div id="klima1" class="accordion-collapse collapse" data-bs-parent="#klimaAccordion">
                <div class="accordion-body" style="background: #f8fafc; padding: 25px; color: #4b5563; line-height: 1.7;">
                  Klima bakımı <strong class="text-primary">yılda en az bir kez</strong> yapılmalıdır. Yoğun kullanım durumunda 6 ayda bir bakım önerilir.
                  Düzenli bakım ile %30'a kadar enerji tasarrufu sağlanır, cihazın ömrü uzar ve daha sağlıklı hava alırsınız.
                  Bakım işlemi filtre temizliği, gaz kontrolü, elektrik bağlantıları kontrolü ve genel performans testini içerir.
                </div>
              </div>
            </div>

            <div class="accordion-item border-0 mb-3 rounded-4 overflow-hidden" style="box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
              <h2 class="accordion-header">
                <button class="accordion-button collapsed fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#klima2" style="background: white; border: none; padding: 20px; font-size: 1.1rem; color: #111827;">
                  <i class="bi bi-clock me-3 text-primary"></i>
                  Klima montajı ne kadar sürer?
                </button>
              </h2>
              <div id="klima2" class="accordion-collapse collapse" data-bs-parent="#klimaAccordion">
                <div class="accordion-body" style="background: #f8fafc; padding: 25px; color: #4b5563; line-height: 1.7;">
                  Standart split klima montajı <strong class="text-primary">2-4 saat</strong> arasında tamamlanır. Süre, montaj yerinin durumu,
                  boru mesafesi ve elektrik altyapısına bağlı olarak değişebilir. VRF sistemler gibi büyük projeler
                  1-3 gün sürebilir. Montaj öncesi ücretsiz keşif yaparak size net süre bilgisi veririz.
                </div>
              </div>
            </div>

            <div class="accordion-item border-0 mb-3 rounded-4 overflow-hidden" style="box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
              <h2 class="accordion-header">
                <button class="accordion-button collapsed fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#klima3" style="background: white; border: none; padding: 20px; font-size: 1.1rem; color: #111827;">
                  <i class="bi bi-droplet me-3 text-primary"></i>
                  Klima gaz dolumu ne zaman gerekir?
                </button>
              </h2>
              <div id="klima3" class="accordion-collapse collapse" data-bs-parent="#klimaAccordion">
                <div class="accordion-body" style="background: #f8fafc; padding: 25px; color: #4b5563; line-height: 1.7;">
                  Klima yeterince soğutmuyorsa, buzlanma varsa veya anormal sesler çıkarıyorsa gaz dolumu gerekebilir.
                  Normal şartlarda klimalar 5-10 yıl gaz kaybetmez. Gaz kaybı genellikle <strong class="text-primary">boru bağlantılarındaki kaçaklar</strong>
                  nedeniyle olur. Önce kaçak tespiti yapılır, onarım sonrası gaz dolumu gerçekleştirilir.
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Kombi Soruları -->
        <div class="mb-5" data-aos="fade-up">
          <div class="d-flex align-items-center mb-4">
            <div class="category-icon me-3 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px; background: linear-gradient(135deg, #ef4444, #dc2626); border-radius: 12px;">
              <i class="bi bi-fire text-white fs-5"></i>
            </div>
            <h3 class="h3 fw-bold text-danger mb-0">Kombi Hizmetleri</h3>
          </div>

          <div class="accordion" id="kombiAccordion">
            <div class="accordion-item border-0 mb-3 rounded-4 overflow-hidden" style="box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
              <h2 class="accordion-header">
                <button class="accordion-button collapsed fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#kombi1" style="background: white; border: none; padding: 20px; font-size: 1.1rem; color: #111827;">
                  <i class="bi bi-calendar-check me-3 text-danger"></i>
                  Kombi bakımı ne zaman yaptırmalıyım?
                </button>
              </h2>
              <div id="kombi1" class="accordion-collapse collapse" data-bs-parent="#kombiAccordion">
                <div class="accordion-body" style="background: #f8fafc; padding: 25px; color: #4b5563; line-height: 1.7;">
                  Kombi bakımı <strong class="text-danger">yılda bir kez, kış öncesi Eylül-Ekim aylarında</strong> yaptırılmalıdır.
                  Bakım işlemi yanma odası temizliği, baca kontrolü, güvenlik sistemleri testi, basınç ayarları
                  ve genel performans kontrolünü içerir. Düzenli bakım ile %20 enerji tasarrufu sağlanır ve arıza riski azalır.
                </div>
              </div>
            </div>

            <div class="accordion-item border-0 mb-3 rounded-4 overflow-hidden" style="box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
              <h2 class="accordion-header">
                <button class="accordion-button collapsed fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#kombi2" style="background: white; border: none; padding: 20px; font-size: 1.1rem; color: #111827;">
                  <i class="bi bi-exclamation-triangle me-3 text-danger"></i>
                  Kombim çalışmıyor, ne yapmalıyım?
                </button>
              </h2>
              <div id="kombi2" class="accordion-collapse collapse" data-bs-parent="#kombiAccordion">
                <div class="accordion-body" style="background: #f8fafc; padding: 25px; color: #4b5563; line-height: 1.7;">
                  Önce <strong class="text-danger">elektrik, gaz ve su bağlantılarını</strong> kontrol edin. Kombi ekranında hata kodu varsa not alın.
                  Basınç göstergesi 1-2 bar arasında olmalı. Bu kontrolleri yaptıktan sonra hala çalışmıyorsa
                  <strong class="text-danger">7/24 acil servisimizi arayın</strong>. Kendi başınıza müdahale etmeyin, güvenlik riski oluşturabilir.
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- İnşaat Soruları -->
        <div class="mb-5" data-aos="fade-up">
          <div class="d-flex align-items-center mb-4">
            <div class="category-icon me-3 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px; background: linear-gradient(135deg, #10b981, #059669); border-radius: 12px;">
              <i class="bi bi-building text-white fs-5"></i>
            </div>
            <h3 class="h3 fw-bold text-success mb-0">İnşaat & Tadilat</h3>
          </div>

          <div class="accordion" id="insaatAccordion">
            <div class="accordion-item border-0 mb-3 rounded-4 overflow-hidden" style="box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
              <h2 class="accordion-header">
                <button class="accordion-button collapsed fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#insaat1" style="background: white; border: none; padding: 20px; font-size: 1.1rem; color: #111827;">
                  <i class="bi bi-diagram-3 me-3 text-success"></i>
                  İnşaat projesi süreci nasıl işliyor?
                </button>
              </h2>
              <div id="insaat1" class="accordion-collapse collapse" data-bs-parent="#insaatAccordion">
                <div class="accordion-body" style="background: #f8fafc; padding: 25px; color: #4b5563; line-height: 1.7;">
                  Süreç şu aşamalardan oluşur: <strong class="text-success">1) Ücretsiz keşif ve ölçüm</strong>, 2) Proje tasarımı ve malzeme seçimi,
                  3) Detaylı fiyat teklifi, 4) Sözleşme imzalama, 5) Malzeme tedariki, 6) Uygulama, 7) Teslim ve garanti.
                  Tüm süreç boyunca düzenli bilgilendirme yapılır ve şeffaf çalışılır.
                </div>
              </div>
            </div>

            <div class="accordion-item border-0 mb-3 rounded-4 overflow-hidden" style="box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
              <h2 class="accordion-header">
                <button class="accordion-button collapsed fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#insaat2" style="background: white; border: none; padding: 20px; font-size: 1.1rem; color: #111827;">
                  <i class="bi bi-house-check me-3 text-success"></i>
                  Tadilat sırasında evde kalabilir miyim?
                </button>
              </h2>
              <div id="insaat2" class="accordion-collapse collapse" data-bs-parent="#insaatAccordion">
                <div class="accordion-body" style="background: #f8fafc; padding: 25px; color: #4b5563; line-height: 1.7;">
                  Tadilat türüne bağlı olarak değişir. <strong class="text-success">Kısmi tadilatlarda</strong> (banyo, mutfak) evde kalabilirsiniz.
                  Kapsamlı tadilatlarda gürültü, toz ve güvenlik nedeniyle geçici taşınma önerilir.
                  Planlama aşamasında size en uygun çözümü birlikte belirleriz ve minimum rahatsızlık için çalışırız.
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Fiyat & Garanti Soruları -->
        <div class="mb-5" data-aos="fade-up">
          <div class="d-flex align-items-center mb-4">
            <div class="category-icon me-3 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px; background: linear-gradient(135deg, #f59e0b, #d97706); border-radius: 12px;">
              <i class="bi bi-currency-dollar text-white fs-5"></i>
            </div>
            <h3 class="h3 fw-bold text-warning mb-0">Fiyat & Garanti</h3>
          </div>

          <div class="accordion" id="fiyatAccordion">
            <div class="accordion-item border-0 mb-3 rounded-4 overflow-hidden" style="box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
              <h2 class="accordion-header">
                <button class="accordion-button collapsed fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#fiyat1" style="background: white; border: none; padding: 20px; font-size: 1.1rem; color: #111827;">
                  <i class="bi bi-calculator me-3 text-warning"></i>
                  Fiyat teklifi nasıl alırım?
                </button>
              </h2>
              <div id="fiyat1" class="accordion-collapse collapse" data-bs-parent="#fiyatAccordion">
                <div class="accordion-body" style="background: #f8fafc; padding: 25px; color: #4b5563; line-height: 1.7;">
                  <strong class="text-warning">Ücretsiz keşif</strong> için bizi arayın veya online form doldurun. Uzman ekibimiz yerinde inceleme yapar,
                  ihtiyaçlarınızı dinler ve 24 saat içinde detaylı fiyat teklifi sunar. Teklif malzeme, işçilik ve
                  garanti detaylarını içerir. Keşif ücretsizdir ve hiçbir yükümlülük gerektirmez.
                </div>
              </div>
            </div>

            <div class="accordion-item border-0 mb-3 rounded-4 overflow-hidden" style="box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
              <h2 class="accordion-header">
                <button class="accordion-button collapsed fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#fiyat2" style="background: white; border: none; padding: 20px; font-size: 1.1rem; color: #111827;">
                  <i class="bi bi-shield-check me-3 text-warning"></i>
                  Garanti süreleri nedir?
                </button>
              </h2>
              <div id="fiyat2" class="accordion-collapse collapse" data-bs-parent="#fiyatAccordion">
                <div class="accordion-body" style="background: #f8fafc; padding: 25px; color: #4b5563; line-height: 1.7;">
                  <strong class="text-warning">Klima montajı:</strong> 2 yıl işçilik garantisi, <strong class="text-warning">Kombi bakımı:</strong> 1 yıl garanti,
                  <strong class="text-warning">İnşaat işleri:</strong> 2-5 yıl (işin türüne göre), <strong class="text-warning">Boya işleri:</strong> 2 yıl garanti.
                  Tüm garantiler yazılı olarak verilir ve garanti kapsamında ücretsiz müdahale yapılır.
                </div>
              </div>
            </div>

            <div class="accordion-item border-0 mb-3 rounded-4 overflow-hidden" style="box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
              <h2 class="accordion-header">
                <button class="accordion-button collapsed fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#fiyat3" style="background: white; border: none; padding: 20px; font-size: 1.1rem; color: #111827;">
                  <i class="bi bi-clock-history me-3 text-warning"></i>
                  Acil durumlarda hizmet veriyor musunuz?
                </button>
              </h2>
              <div id="fiyat3" class="accordion-collapse collapse" data-bs-parent="#fiyatAccordion">
                <div class="accordion-body" style="background: #f8fafc; padding: 25px; color: #4b5563; line-height: 1.7;">
                  Evet, <strong class="text-warning">7/24 acil servis</strong> hizmeti veriyoruz. Klima ve kombi arızaları, su kaçakları,
                  elektrik kesintileri gibi acil durumlar için haftanın her günü, günün her saati ulaşabilirsiniz.
                  Acil müdahale ücreti standart fiyatlarımıza %50 ek ücret ile hesaplanır.
                </div>
              </div>
            </div>
          </div>
        </div>

        <div style="background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.08); margin-bottom: 15px;">
          <div style="padding: 20px; border-bottom: 1px solid #e5e7eb; cursor: pointer;" onclick="toggleFaq('faq6')">
            <div style="display: flex; justify-content: between; align-items: center;">
              <h4 style="color: #111827; margin: 0; font-size: 1.1rem;">İnşaat projesi süreci nasıl işliyor?</h4>
              <span id="faq6-icon" style="color: #10b981; font-size: 1.2rem;">+</span>
            </div>
          </div>
          <div id="faq6-content" style="display: none; padding: 20px; background: #f8fafc;">
            <p style="color: #4b5563; line-height: 1.6; margin: 0;">
              Süreç şu aşamalardan oluşur: <strong>1) Ücretsiz keşif ve ölçüm</strong>, 2) Proje tasarımı ve malzeme seçimi,
              3) Detaylı fiyat teklifi, 4) Sözleşme imzalama, 5) Malzeme tedariki, 6) Uygulama, 7) Teslim ve garanti.
              Tüm süreç boyunca düzenli bilgilendirme yapılır ve şeffaf çalışılır.
            </p>
          </div>
        </div>

        <div style="background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.08); margin-bottom: 15px;">
          <div style="padding: 20px; border-bottom: 1px solid #e5e7eb; cursor: pointer;" onclick="toggleFaq('faq7')">
            <div style="display: flex; justify-content: between; align-items: center;">
              <h4 style="color: #111827; margin: 0; font-size: 1.1rem;">Tadilat sırasında evde kalabilir miyim?</h4>
              <span id="faq7-icon" style="color: #10b981; font-size: 1.2rem;">+</span>
            </div>
          </div>
          <div id="faq7-content" style="display: none; padding: 20px; background: #f8fafc;">
            <p style="color: #4b5563; line-height: 1.6; margin: 0;">
              Tadilat türüne bağlı olarak değişir. <strong>Kısmi tadilatlarda</strong> (banyo, mutfak) evde kalabilirsiniz.
              Kapsamlı tadilatlarda gürültü, toz ve güvenlik nedeniyle geçici taşınma önerilir.
              Planlama aşamasında size en uygun çözümü birlikte belirleriz ve minimum rahatsızlık için çalışırız.
            </p>
          </div>
        </div>
      </div>

      <!-- Fiyat & Garanti Soruları -->
      <div style="margin-bottom: 40px;" data-aos="fade-up">
        <h3 style="color: #f59e0b; margin-bottom: 25px; font-size: 1.8rem; display: flex; align-items: center; gap: 10px;">
          💰 Fiyat & Garanti
        </h3>

        <div style="background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.08); margin-bottom: 15px;">
          <div style="padding: 20px; border-bottom: 1px solid #e5e7eb; cursor: pointer;" onclick="toggleFaq('faq8')">
            <div style="display: flex; justify-content: between; align-items: center;">
              <h4 style="color: #111827; margin: 0; font-size: 1.1rem;">Fiyat teklifi nasıl alırım?</h4>
              <span id="faq8-icon" style="color: #f59e0b; font-size: 1.2rem;">+</span>
            </div>
          </div>
          <div id="faq8-content" style="display: none; padding: 20px; background: #f8fafc;">
            <p style="color: #4b5563; line-height: 1.6; margin: 0;">
              <strong>Ücretsiz keşif</strong> için bizi arayın veya online form doldurun. Uzman ekibimiz yerinde inceleme yapar,
              ihtiyaçlarınızı dinler ve 24 saat içinde detaylı fiyat teklifi sunar. Teklif malzeme, işçilik ve
              garanti detaylarını içerir. Keşif ücretsizdir ve hiçbir yükümlülük gerektirmez.
            </p>
          </div>
        </div>

        <div style="background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.08); margin-bottom: 15px;">
          <div style="padding: 20px; border-bottom: 1px solid #e5e7eb; cursor: pointer;" onclick="toggleFaq('faq9')">
            <div style="display: flex; justify-content: between; align-items: center;">
              <h4 style="color: #111827; margin: 0; font-size: 1.1rem;">Garanti süreleri nedir?</h4>
              <span id="faq9-icon" style="color: #f59e0b; font-size: 1.2rem;">+</span>
            </div>
          </div>
          <div id="faq9-content" style="display: none; padding: 20px; background: #f8fafc;">
            <p style="color: #4b5563; line-height: 1.6; margin: 0;">
              <strong>Klima montajı:</strong> 2 yıl işçilik garantisi, <strong>Kombi bakımı:</strong> 1 yıl garanti,
              <strong>İnşaat işleri:</strong> 2-5 yıl (işin türüne göre), <strong>Boya işleri:</strong> 2 yıl garanti.
              Tüm garantiler yazılı olarak verilir ve garanti kapsamında ücretsiz müdahale yapılır.
            </p>
          </div>
        </div>

        <div style="background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.08); margin-bottom: 15px;">
          <div style="padding: 20px; border-bottom: 1px solid #e5e7eb; cursor: pointer;" onclick="toggleFaq('faq10')">
            <div style="display: flex; justify-content: between; align-items: center;">
              <h4 style="color: #111827; margin: 0; font-size: 1.1rem;">Acil durumlarda hizmet veriyor musunuz?</h4>
              <span id="faq10-icon" style="color: #f59e0b; font-size: 1.2rem;">+</span>
            </div>
          </div>
          <div id="faq10-content" style="display: none; padding: 20px; background: #f8fafc;">
            <p style="color: #4b5563; line-height: 1.6; margin: 0;">
              Evet, <strong>7/24 acil servis</strong> hizmeti veriyoruz. Klima ve kombi arızaları, su kaçakları,
              elektrik kesintileri gibi acil durumlar için haftanın her günü, günün her saati ulaşabilirsiniz.
              Acil müdahale ücreti standart fiyatlarımıza %50 ek ücret ile hesaplanır.
            </p>
          </div>
        </div>
      </div>

    </div>
  </div>
</section>

<!-- Modern CTA Section -->
<section class="py-5 text-white text-center position-relative overflow-hidden" style="background: linear-gradient(135deg, #6366f1 0%, #4f46e5 50%, #4338ca 100%);">
  <!-- Animated Background -->
  <div class="position-absolute top-0 start-0 w-100 h-100" style="opacity: 0.1;">
    <div class="position-absolute d-none d-lg-block" style="top: 20%; left: 10%; width: 150px; height: 150px; background: radial-gradient(circle, #ffffff 0%, transparent 70%); border-radius: 50%; animation: float 8s ease-in-out infinite;"></div>
    <div class="position-absolute d-none d-lg-block" style="bottom: 20%; right: 15%; width: 120px; height: 120px; background: radial-gradient(circle, #fbbf24 0%, transparent 70%); border-radius: 50%; animation: float 6s ease-in-out infinite reverse;"></div>
  </div>

  <div class="container position-relative" style="z-index: 2;" data-aos="fade-up">
    <div class="row justify-content-center">
      <div class="col-lg-8">
        <div class="mb-4">
          <i class="bi bi-chat-dots fs-1 text-warning mb-3"></i>
        </div>
        <h2 class="h2 fw-bold mb-3">Hala Sorunuz Var mı?</h2>
        <p class="fs-5 mb-4" style="opacity: 0.9; max-width: 600px; margin: 0 auto;">
          Aradığınız soruyu bulamadıysanız, bizimle iletişime geçin. Uzman ekibimiz size yardımcı olmaktan mutluluk duyar.
        </p>

        <div class="d-flex gap-3 justify-content-center flex-wrap">
          <a href="tel:05301799558" class="btn btn-lg fw-bold px-4 py-3 rounded-4 d-flex align-items-center" style="background: linear-gradient(135deg, #fbbf24, #f59e0b); color: #111827; box-shadow: 0 8px 25px rgba(251, 191, 36, 0.4); text-decoration: none;">
            <i class="bi bi-telephone-fill me-2"></i>
            Hemen Arayın
          </a>
          <a href="{{ url_for('iletisim') }}" class="btn btn-outline-light btn-lg fw-bold px-4 py-3 rounded-4 d-flex align-items-center" style="border: 2px solid white; text-decoration: none;">
            <i class="bi bi-envelope me-2"></i>
            Mesaj Gönderin
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Modern CSS for Category Cards -->
<style>
.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0,0,0,0.15) !important;
}

.category-card:hover .category-icon {
  transform: scale(1.1);
}

.accordion-button:not(.collapsed) {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(99, 102, 241, 0.05)) !important;
  color: #4338ca !important;
}

.accordion-button:focus {
  box-shadow: none !important;
  border: none !important;
}

.accordion-button::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%234338ca'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e") !important;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.search-tag:hover {
  background: #6366f1 !important;
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.category-filter.active {
  background: #6366f1 !important;
  color: white !important;
  border-color: #6366f1 !important;
}

.faq-item {
  transition: all 0.3s ease;
}

.faq-item.hidden {
  display: none !important;
}

#searchInput:focus {
  border-color: #6366f1 !important;
  box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25) !important;
}
</style>

<script>
// Arama fonksiyonu
function searchFAQ(query = null) {
  const searchInput = document.getElementById('searchInput');
  const searchTerm = query || searchInput.value.toLowerCase().trim();
  const faqItems = document.querySelectorAll('.faq-item');
  const searchResults = document.getElementById('searchResults');
  const searchResultText = document.getElementById('searchResultText');

  if (query) {
    searchInput.value = query;
  }

  if (searchTerm === '') {
    // Arama temizlendiğinde tüm soruları göster
    faqItems.forEach(item => {
      item.classList.remove('hidden');
    });
    searchResults.style.display = 'none';
    return;
  }

  let foundCount = 0;

  faqItems.forEach(item => {
    const questionText = item.querySelector('.accordion-button').textContent.toLowerCase();
    const answerText = item.querySelector('.accordion-body').textContent.toLowerCase();

    if (questionText.includes(searchTerm) || answerText.includes(searchTerm)) {
      item.classList.remove('hidden');
      foundCount++;
    } else {
      item.classList.add('hidden');
    }
  });

  // Arama sonuçlarını göster
  searchResults.style.display = 'block';
  if (foundCount > 0) {
    searchResultText.textContent = `"${searchTerm}" için ${foundCount} sonuç bulundu`;
  } else {
    searchResultText.textContent = `"${searchTerm}" için sonuç bulunamadı`;
  }

  // Kategori filtrelerini sıfırla
  document.querySelectorAll('.category-filter').forEach(btn => {
    btn.classList.remove('active');
  });
  document.querySelector('[data-category="all"]').classList.add('active');
}

// Arama temizleme
function clearSearch() {
  document.getElementById('searchInput').value = '';
  document.getElementById('searchResults').style.display = 'none';
  document.querySelectorAll('.faq-item').forEach(item => {
    item.classList.remove('hidden');
  });
}

// Kategori filtreleme
function filterByCategory(category) {
  const faqItems = document.querySelectorAll('.faq-item');

  // Arama sonuçlarını gizle
  document.getElementById('searchResults').style.display = 'none';
  document.getElementById('searchInput').value = '';

  faqItems.forEach(item => {
    if (category === 'all' || item.dataset.category === category) {
      item.classList.remove('hidden');
    } else {
      item.classList.add('hidden');
    }
  });

  // Aktif kategori butonunu güncelle
  document.querySelectorAll('.category-filter').forEach(btn => {
    btn.classList.remove('active');
  });
  document.querySelector(`[data-category="${category}"]`).classList.add('active');
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
  // Arama input'u için event listener
  const searchInput = document.getElementById('searchInput');
  searchInput.addEventListener('input', () => searchFAQ());
  searchInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      e.preventDefault();
      searchFAQ();
    }
  });

  // Kategori filtreleri için event listeners
  document.querySelectorAll('.category-filter').forEach(btn => {
    btn.addEventListener('click', function() {
      const category = this.dataset.category;
      filterByCategory(category);
    });
  });

  // FAQ item'larına data-category ekle
  document.querySelectorAll('#klimaAccordion .accordion-item').forEach(item => {
    item.classList.add('faq-item');
    item.dataset.category = 'klima';
  });

  document.querySelectorAll('#kombiAccordion .accordion-item').forEach(item => {
    item.classList.add('faq-item');
    item.dataset.category = 'kombi';
  });

  document.querySelectorAll('#insaatAccordion .accordion-item').forEach(item => {
    item.classList.add('faq-item');
    item.dataset.category = 'insaat';
  });

  document.querySelectorAll('#fiyatAccordion .accordion-item').forEach(item => {
    item.classList.add('faq-item');
    item.dataset.category = 'fiyat';
  });
});
</script>

{% endblock %}
