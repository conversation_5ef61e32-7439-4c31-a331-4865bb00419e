# Hizmet Sayfaları Arka Plan Resimleri

Bu klasöre aşağıdaki PNG dosyalarını eklemeniz gerekmektedir:

## Gerekli Dosyalar:

### 1. klima-bg.png
- **Kullanım:** Klima servisi sayfası hero arka planı
- **Sayfa:** /klima
- **Önerilen Boyut:** 1920x1080px
- **İçerik:** <PERSON>lim<PERSON>, so<PERSON>utma, havalandırma ile ilgili görsel

### 2. kombi-bg.png  
- **Kullanım:** Kombi servisi sayfası hero arka planı
- **Sayfa:** /kombi
- **Önerilen Boyut:** 1920x1080px
- **İçerik:** Kombi, ısıtma, doğalgaz ile ilgili görsel

### 3. insaat-bg.png
- **Kullanım:** İnşaat hizmetleri sayfası hero arka planı
- **Sayfa:** /insaat
- **Önerilen Boyut:** 1920x1080px
- **İçerik:** <PERSON>n<PERSON><PERSON><PERSON>, tadilat, renovasyon ile ilgili görsel

### 4. boya-bg.png
- **Kullanım:** Boya & Badana sayfası hero arka planı
- **Sayfa:** /boya-badana
- **Önerilen Boyut:** 1920x1080px
- **İçerik:** Boya, badana, renk paleti ile ilgili görsel

### 5. iletisim.png (Zaten mevcut)
- **Kullanım:** İletişim sayfası hero arka planı
- **Sayfa:** /iletisim
- **Durum:** ✅ Mevcut

### 6. hakkimizda.png (Zaten mevcut)
- **Kullanım:** Hakkımızda sayfası hero arka planı
- **Sayfa:** /hakkimizda
- **Durum:** ✅ Mevcut

## Teknik Özellikler:

- **Format:** PNG (şeffaflık desteği için)
- **Boyut:** 1920x1080px (Full HD)
- **Kalite:** Yüksek çözünürlük
- **Dosya Boyutu:** Maksimum 2MB (web optimizasyonu için)
- **Renk Profili:** sRGB

## Tasarım Önerileri:

- Gradient overlay ile uyumlu olacak şekilde tasarlayın
- Metin okunabilirliğini engellemeyecek kontrast
- Hizmetle ilgili görsel elementler içermeli
- Modern ve profesyonel görünüm
- Mobil uyumlu (responsive) tasarım

## Gradient Overlay Bilgileri:

### Klima (Mavi)
```css
background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(30, 64, 175, 0.8) 50%, rgba(29, 78, 216, 0.8) 100%)
```

### Kombi (Kırmızı)
```css
background: linear-gradient(135deg, rgba(239, 68, 68, 0.8) 0%, rgba(220, 38, 38, 0.8) 50%, rgba(185, 28, 28, 0.8) 100%)
```

### İnşaat (Yeşil)
```css
background: linear-gradient(135deg, rgba(16, 185, 129, 0.8) 0%, rgba(5, 150, 105, 0.8) 50%, rgba(4, 120, 87, 0.8) 100%)
```

### Boya (Turuncu)
```css
background: linear-gradient(135deg, rgba(245, 158, 11, 0.8) 0%, rgba(217, 119, 6, 0.8) 50%, rgba(180, 83, 9, 0.8) 100%)
```

## Kullanım:

Dosyaları `static/img/` klasörüne ekledikten sonra sayfalar otomatik olarak bu arka plan resimlerini kullanacaktır.
