#!/usr/bin/env python3
"""
Production runner for Şenkal Grup website
This script ensures the application starts correctly with proper path configuration
"""

import os
import sys

# Ensure we're in the correct directory
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# Add current directory to Python path
if script_dir not in sys.path:
    sys.path.insert(0, script_dir)

# Import and run the application
if __name__ == "__main__":
    try:
        from app import app
        print(f"Starting application from: {script_dir}")
        print(f"Flask app: {app}")
        app.run(host="0.0.0.0", port=8000, debug=False)
    except ImportError as e:
        print(f"Error importing app: {e}")
        print(f"Current directory: {os.getcwd()}")
        print(f"Script directory: {script_dir}")
        print(f"Python path: {sys.path}")
        print(f"Files in directory: {os.listdir('.')}")
        sys.exit(1)
