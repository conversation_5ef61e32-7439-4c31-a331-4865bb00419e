#!/usr/bin/env python3
"""
WSGI entry point for Şenkal Grup website
"""

import sys
import os

# Add the project directory to Python path
project_dir = os.path.dirname(os.path.abspath(__file__))
if project_dir not in sys.path:
    sys.path.insert(0, project_dir)

try:
    # Import the Flask application
    from app import app

    # WSGI application
    application = app

    # For debugging
    print(f"Successfully imported app from: {project_dir}")
    print(f"Python path: {sys.path}")

except ImportError as e:
    print(f"Error importing app: {e}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Project directory: {project_dir}")
    print(f"Python path: {sys.path}")
    print(f"Files in project directory: {os.listdir(project_dir)}")
    raise

if __name__ == "__main__":
    application.run()
