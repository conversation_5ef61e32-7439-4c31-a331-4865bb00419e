# Şenkal Grup Website

Bu proje Şenkal Grup'un kurumsal web sitesidir. Flask framework'ü kullanılarak geliştirilmiştir.

## Kurulum ve Çalıştırma

### Gereksinimler
- Python 3.8+
- pip (Python paket yöneticisi)

### Hızlı Başlangıç

1. **Bağımlılıkları yükleyin:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Uygulamayı çalıştırın:**
   
   **Seçenek 1: Basit mod (Geliştirme)**
   ```bash
   ./start_simple.sh
   ```
   veya
   ```bash
   python3 app.py
   ```

   **Seçenek 2: <PERSON><PERSON><PERSON> modu (Gunicorn ile)**
   ```bash
   ./start.sh
   ```

### Manuel Çalıştırma

Eğer scriptler çalışmıyorsa, manuel olarak şu komutları kullanabilirsiniz:

```bash
# Doğru dizinde olduğunuzdan emin olun
cd /path/to/senkalgrup

# Python path'ini ayarlayın
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Gunicorn ile çalıştırın
gunicorn --bind 0.0.0.0:8000 --chdir $(pwd) wsgi:application

# Veya direkt Python ile
python3 app.py
```

## Hata Giderme

### "ModuleNotFoundError: No module named 'app'" Hatası

Bu hata genellikle şu nedenlerle oluşur:

1. **Yanlış dizin:** Uygulama doğru dizinden çalıştırılmıyor
   ```bash
   # Doğru dizine gidin
   cd /path/to/senkalgrup
   ls -la  # app.py dosyasının olduğunu kontrol edin
   ```

2. **Python path sorunu:** Python, app.py dosyasını bulamıyor
   ```bash
   # Python path'ini kontrol edin
   python3 -c "import sys; print(sys.path)"
   
   # Mevcut dizini path'e ekleyin
   export PYTHONPATH="${PYTHONPATH}:$(pwd)"
   ```

3. **Sanal ortam sorunu:** Yanlış Python ortamı kullanılıyor
   ```bash
   # Sanal ortamı aktifleştirin (varsa)
   source venv/bin/activate
   # veya
   source .venv/bin/activate
   ```

### Gunicorn Sorunları

1. **Konfigürasyon dosyası ile çalıştırın:**
   ```bash
   gunicorn --config gunicorn.conf.py wsgi:application
   ```

2. **Verbose mod ile hata detaylarını görün:**
   ```bash
   gunicorn --bind 0.0.0.0:8000 --log-level debug wsgi:application
   ```

3. **Çalışma dizinini belirtin:**
   ```bash
   gunicorn --bind 0.0.0.0:8000 --chdir /path/to/senkalgrup wsgi:application
   ```

### Test Komutları

Uygulamanın doğru çalışıp çalışmadığını test etmek için:

```bash
# Python import testı
python3 -c "from app import app; print('✓ Import başarılı')"

# WSGI import testı
python3 -c "from wsgi import application; print('✓ WSGI import başarılı')"

# Flask app testı
python3 -c "
from app import app
with app.test_client() as client:
    response = client.get('/')
    print(f'✓ Ana sayfa yanıtı: {response.status_code}')
"
```

## Dosya Yapısı

```
senkalgrup/
├── app.py              # Ana Flask uygulaması
├── wsgi.py             # WSGI giriş noktası
├── gunicorn.conf.py    # Gunicorn konfigürasyonu
├── run.py              # Üretim çalıştırıcısı
├── start.sh            # Gunicorn başlatma scripti
├── start_simple.sh     # Basit başlatma scripti
├── requirements.txt    # Python bağımlılıkları
├── static/             # Statik dosyalar (CSS, JS, resimler)
├── templates/          # HTML şablonları
└── README.md           # Bu dosya
```

## Port ve Erişim

- **Geliştirme modu:** http://localhost:8000
- **Üretim modu:** http://0.0.0.0:8000

## Loglar

Gunicorn logları varsayılan olarak stdout/stderr'a yazılır. Detaylı loglar için:

```bash
gunicorn --access-logfile access.log --error-logfile error.log wsgi:application
```

## Destek

Sorun yaşıyorsanız:

1. Bu README'deki hata giderme adımlarını takip edin
2. Log dosyalarını kontrol edin
3. Test komutlarını çalıştırın
4. Gerekirse geliştirici ile iletişime geçin
